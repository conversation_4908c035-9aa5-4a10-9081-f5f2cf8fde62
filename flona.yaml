project_name: flona
run_name: flona

# =====training setup=====
use_wandb: false # set to false if you don't want to log to wandb  1
train: True
batch_size: 4  #1
epochs: 10
gpu_ids: [0]     # 1
num_workers: 4  #1
lr: 1e-4
optimizer: adamw
max_norm: 1.
scheduler: "cosine"
warmup: True 
warmup_epochs: 4
seed: 0
# load_run: "flona/flona_2024_10_19_09_11_57"

# =====model params=====
vision_encoder: flona_vint
encoding_size: 256 # 1
obs_encoder: efficientnet-b0
cond_predict_scale: False
mha_num_attention_heads: 4 #  1
mha_num_attention_layers: 4  # 1
mha_ff_dim_factor: 4
down_dims: [64, 128, 256]
num_diffusion_iters: 10
normalize: True  #1
context_size: 3 # 5 1
alpha: 1e-3
len_traj_pred: 32 #1

# dataset specific parameters
image_size: [96, 96] # width, height  
datasets:
  data_folder: 'datasets/scenes_117'
  trav_map_folder: 'datasets/trav_maps'
  end_slack: 3 
  waypoint_spacing: 1  # 1
  scene_names: [<PERSON><PERSON><PERSON>_0, Hillsdale_1, Mosquito_1, Roane_0, Stilwell_0]
  # [Albertville_0, Mosinee_0, Pleasant_0, Roxboro_0, Spencerville_0, Albertville_1, Azusa_1, Colebrook_0, Eagerville_0, Hainesburg_1, Lovilia_0, Mosquito_0, Plessis_0,  Eastville_0, Hambleton_0, Lovilia_1, Mosquito_1, Quantico_0, Sanctuary_0, Springhill_0, Anaheim_1, Ballou_1, Convoy_0, Edgemere_0, Haxtun_0, Maben_0, Nicut_0, Rabbit_0, Sands_0, Crandon_0, Espanola_0,  Sumas_0, Angiola_0, Bonfield_0, Crandon_1, Eudora_0, Hometown_0, Mesic_0, Nuevo_0, Ribera_0, Seward_0, Superior_0, Mesic_1, Oyens_0, Roane_0, Shelbiana_0, Cantwell_0, Delton_0, Goffs_0, Kerrtown_0, Monson_0, Parole_0, Roeville_0, Silas_0, Arkansaw_1, Capistrano_0, Denmark_0, Goffs_1, Ladue_0, Monson_1, Dryville_0, Greigsville_0, Ladue_1, Morris_0, Placida_0, Rosser_0, Sodaville_0] #67
  # [Albertville_0, Mosinee_0, Pleasant_0, Roxboro_0, Spencerville_0, Albertville_1, Azusa_1, Colebrook_0, Eagerville_0, Hainesburg_1, Lovilia_0, Mosquito_0, Plessis_0,  Eastville_0, Hambleton_0, Lovilia_1, Mosquito_1, Quantico_0, Sanctuary_0, Springhill_0, Anaheim_1, Ballou_1, Convoy_0, Edgemere_0, Haxtun_0, Maben_0, Nicut_0, Rabbit_0, Sands_0, Crandon_0, Espanola_0,  Sumas_0, Angiola_0, Bonfield_0, Crandon_1, Eudora_0, Hometown_0, Mesic_0, Nuevo_0, Ribera_0, Seward_0, Superior_0, Mesic_1, Oyens_0, Roane_0, Shelbiana_0, Cantwell_0, Delton_0, Goffs_0, Kerrtown_0, Monson_0, Parole_0, Roeville_0, Silas_0, Arkansaw_1, Capistrano_0, Denmark_0, Goffs_1, Ladue_0, Monson_1, Dryville_0, Greigsville_0, Ladue_1, Morris_0, Placida_0, Rosser_0, Sodaville_0]
  # [Albertville_0, Azusa_1, Eagerville_0,  Plessis_0,  Eastville_0, Hambleton_0, Quantico_0, Sanctuary_0, Springhill_0, Sands_0, Crandon_0, Espanola_0, Nuevo_0, Ribera_0, Seward_0, Superior_0, Mesic_1, Goffs_0, Kerrtown_0, Monson_0, Roeville_0, Silas_0, Denmark_0, Goffs_1, Ladue_0, Ladue_1, Morris_0, Placida_0, Rosser_0, Sodaville_0] 30
  # [Albertville_0, Caruthers_0, Dunmor_0, Hainesburg_0, Lathrup_0, Mosinee_0, Pleasant_0, Roxboro_0, Spencerville_0, Albertville_1, Azusa_1, Colebrook_0, Hainesburg_1, Lovilia_0, Mosquito_0, Plessis_0, Samuels_0, Spotswood_0, Anaheim_0, Ballou_0, Connellsville_0, Eastville_0, Hambleton_0, Mosquito_1, Quantico_0, Sanctuary_0, Springhill_0, Anaheim_1, Ballou_1, Convoy_0, Edgemere_0, Haxtun_0, Maben_0, Nicut_0, Sands_0, Stilwell_0, Andover_0, Cooperstown_0, Hillsdale_0, Maryhill_0, Nimmons_0, Sasakwa_0, Andover_1, Bolton_0, Crandon_0, Espanola_0, Hillsdale_1, Matoaca_0, Norvelt_0, Reyno_0, Sawpit_0, Angiola_0, Crandon_1, Eudora_0, Hometown_0, Mesic_0, Nuevo_0, Ribera_0, Seward_0, Superior_0, Annawan_0, Dauberville_0, Ewansville_0, Hominy_0, Mesic_1, Oyens_0, Roane_0, Shelbiana_0, Swormville_0, Applewold_1, Brevort_0, Dauberville_1, Hominy_1, Mobridge_0, Pablo_0, Rockport_0, Shingler_0, Woonsocket_0, Arkansaw_0, Cantwell_0, Delton_0, Kerrtown_0, Parole_0, Roeville_0, Arkansaw_1, Capistrano_0, Denmark_0, Goffs_1, Ladue_0, Monson_1, Pettigrew_0, Sisters_0, Capistrano_1, Dryville_0, Greigsville_0, Ladue_1, Morris_0, Placida_0, Rosser_0, Sodaville_0] 100
  # [Albertville_0, Azusa_0, Caruthers_0, Dunmor_0, Hainesburg_0, Lathrup_0, Mosinee_0, Pleasant_0, Roxboro_0, Spencerville_0, Albertville_1, Azusa_1, Colebrook_0, Eagerville_0, Hainesburg_1, Lovilia_0, Mosquito_0, Plessis_0, Samuels_0, Spotswood_0, Anaheim_0, Ballou_0, Connellsville_0, Eastville_0, Hambleton_0, Lovilia_1, Mosquito_1, Quantico_0, Sanctuary_0, Springhill_0, Anaheim_1, Ballou_1, Convoy_0, Edgemere_0, Haxtun_0, Maben_0, Nicut_0, Rabbit_0, Sands_0, Stilwell_0, Andover_0, Beach_0, Cooperstown_0, Elmira_0, Hillsdale_0, Maryhill_0, Nimmons_0, Rancocas_0, Sasakwa_0, Stokes_0, Andover_1, Bolton_0, Crandon_0, Espanola_0, Hillsdale_1, Matoaca_0, Norvelt_0, Reyno_0, Sawpit_0, Sumas_0, Angiola_0, Bonfield_0, Crandon_1, Eudora_0, Hometown_0, Mesic_0, Nuevo_0, Ribera_0, Seward_0, Superior_0, Annawan_0, Brentsville_0, Dauberville_0, Ewansville_0, Hominy_0, Mesic_1, Oyens_0, Roane_0, Shelbiana_0, Swormville_0, Applewold_1, Brevort_0, Dauberville_1, Ewansville_1, Hominy_1, Mobridge_0, Pablo_0, Rockport_0, Shingler_0, Woonsocket_0, Arkansaw_0, Cantwell_0, Delton_0, Goffs_0, Kerrtown_0, Monson_0, Parole_0, Roeville_0, Silas_0, Arkansaw_1, Capistrano_0, Denmark_0, Goffs_1, Ladue_0, Monson_1, Pettigrew_0, Rosenberg_0, Sisters_0, Avonia_0, Capistrano_1, Dryville_0, Greigsville_0, Ladue_1, Morris_0, Placida_0, Rosser_0, Sodaville_0] 117

# logging stuff
print_log_freq: 500 # in iterations

image_log_freq: 500 #0 # in iterations
num_images_log: 8 #0 
pairwise_test_freq: 0 # in epochs
eval_fraction: 0.1
wandb_log_freq: 10 # in iterations
eval_freq: 2 # in epochs

# =====test setup=====
num_samples: 30 