# scene
scene: gibson
# scene_id: Albertville
scene_id: Rs
scene_path: iGibson/igibson/data/g_dataset/
build_graph: true
load_texture: true
pybullet_load_texture: true
trav_map_type: no_obj
trav_map_resolution: 0.1
trav_map_erosion: 2
should_open_all_doors: true

# domain randomization
texture_randomization_freq: null
object_randomization_freq: null

# robot
robot:
  name: Locobot
  action_type: continuous
  action_normalize: true
  base_name: null
  scale: 1.0
  self_collision: false
  rendering_params: null
  controller_config:
    base:
      name: DifferentialDriveController
      command_output_limits:
        - [-0.5, -1.57]
        - [0.5, 1.57]

# task
task: point_nav_random
target_dist_min: 1.0
target_dist_max: 10.0
goal_format: polar
task_obs_dim: 4

# reward
reward_type: geodesic
success_reward: 10.0
potential_reward_weight: 1.0
collision_reward_weight: -0.1

# discount factor
discount_factor: 0.99

# termination condition
dist_tol: 0.36  # body width
max_collisions_allowed: 500
max_step: 3000
# misc config
initial_pos_z_offset: 0.1
collision_ignore_link_a_ids: [1, 2, 3, 4]  # ignore collisions with these robot links

# sensor spec
output: [task_obs, rgb, depth]
# image
# Intel Realsense Depth Camera D435
# https://store.intelrealsense.com/buy-intel-realsense-depth-camera-d435.html
fisheye: false
image_width: 512
image_height: 512
vertical_fov: 42.5
# depth
depth_low : 0.1
depth_high: 10.0

# sensor noise
depth_noise_rate: 0.0

# visual objects
visible_target: true
visible_path: false
